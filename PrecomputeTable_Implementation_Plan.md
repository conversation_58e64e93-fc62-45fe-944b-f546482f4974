# Bernstein-<PERSON>预计算表实现执行计划

**计划版本**: v3.0  
**制定日期**: 2025年1月31日  
**理论基础**: <PERSON>-<PERSON>论文第3.2-3.4节 + 第5节空间优化  
**核心目标**: 实现高效的预计算表生成、存储、查找和优化系统  

---

## 📋 执行摘要

基于Bernstein-Lange论文的预计算表理论，本计划详细规划了预计算表系统的完整实现。核心包括：表生成算法、Distinguished Points选择策略、空间压缩技术、GPU并行化实现等关键技术。

### 核心理论公式回顾
- **表大小**: T = ℓ¹ᐟ³
- **候选点数量**: M = ⌈T·ln(2)⌉ ≈ 1.44T
- **游走长度**: W = α·(ℓ/T)¹ᐟ²，α ≈ 0.786
- **成功概率**: 1 - exp(-α²) ≈ 38.2%（α=0.786时）
- **权重计算**: weight = total_walk_length + 4W·walk_count

---

## 🏗️ 第一阶段：核心算法实现（论文第3.2节）

### 1.1 预计算表生成算法

**理论基础**: 论文第3.2节"The basic algorithm"

```cpp
/**
 * @brief 预计算表生成核心算法
 * 严格按照论文第3.2节实现
 */
bool PrecomputeTable::GenerateTable(const TableGenParams& params) {
    this->params = params;
    
    // 步骤1: 计算参数（基于论文公式）
    uint64_t M = (uint64_t)ceil(params.T * log(2.0));  // M = ⌈T·ln(2)⌉
    uint64_t W = (uint64_t)(params.alpha * sqrt((double)params.ell / params.T));
    
    printf("生成参数: ℓ=%llu, T=%llu, M=%llu, W=%llu, α=%.3f\n", 
           params.ell, params.T, M, W, params.alpha);
    
    // 步骤2: 生成跳跃表（r-adding walks）
    GenerateJumpTable(params.r);
    
    // 步骤3: 并行生成候选区分点
    std::vector<PrecomputeEntry> candidates;
    candidates.reserve(M * 2);  // 预留更多空间
    
    #pragma omp parallel for
    for (uint64_t i = 0; i < M; i++) {
        PrecomputeEntry candidate = GenerateCandidatePoint(i, W);
        if (candidate.hash_value != 0) {  // 有效候选点
            #pragma omp critical
            candidates.push_back(candidate);
        }
    }
    
    printf("生成了 %zu 个候选区分点\n", candidates.size());
    
    // 步骤4: 选择最有用的T个点（论文3.4节）
    entries = SelectMostUseful(candidates, params.T);
    
    // 步骤5: 建立哈希索引
    BuildHashIndex();
    
    is_built = true;
    return true;
}
```

### 1.2 候选点生成算法

**理论基础**: 论文第3.2节游走算法

```cpp
/**
 * @brief 生成单个候选区分点
 * 实现论文中的随机游走算法
 */
PrecomputeEntry PrecomputeTable::GenerateCandidatePoint(uint64_t seed, uint64_t max_walk_length) {
    PrecomputeEntry entry = {0};
    
    // 随机起始点 y_i ∈ [0, ℓ-1]
    Random::SetSeed(seed);
    Int y_start = Random::GetRandomInt(params.ell);
    Point P = secp->ComputePublicKey(&y_start);
    
    uint64_t total_steps = 0;
    uint32_t walk_count = 1;
    Int current_log = y_start;
    
    // 游走直到找到区分点或达到最大步数
    while (total_steps < max_walk_length * 4) {
        // 检查是否为区分点
        if (IsDistinguishedPoint(P)) {
            entry.distinguished_point = P;
            entry.discrete_log = current_log;
            entry.walk_length = total_steps;
            entry.walk_count = walk_count;
            entry.weight = total_steps + 4.0 * max_walk_length * walk_count;
            entry.hash_value = ComputePointHash(P);
            entry.is_compressed = false;
            return entry;
        }
        
        // r-adding walk步进
        uint32_t jump_index = Random::GetRandomInt(jump_table.size());
        P = secp->Add(P, jump_table[jump_index]);
        current_log = current_log.Add(&jump_distances[jump_index]);
        total_steps++;
    }
    
    // 未找到区分点
    return entry;  // hash_value = 0 表示无效
}
```

### 1.3 Distinguished Points检测

**理论基础**: 论文中的SHA256哈希检测机制

```cpp
/**
 * @brief 区分点检测算法
 * 基于论文的SHA256哈希方法
 */
bool PrecomputeTable::IsDistinguishedPoint(const Point& P) {
    uint8_t hash[32];
    
    // 计算点的SHA256哈希
    SHA256_CTX ctx;
    SHA256_Init(&ctx);
    
    // 将点坐标转换为字节数组
    uint8_t x_bytes[32], y_bytes[32];
    P.x.Get32Bytes(x_bytes);
    P.y.Get32Bytes(y_bytes);
    
    SHA256_Update(&ctx, x_bytes, 32);
    SHA256_Update(&ctx, y_bytes, 32);
    SHA256_Final(hash, &ctx);
    
    // 检查前threshold位是否为0
    uint32_t leading_zeros = CountLeadingZeros(hash);
    return leading_zeros >= params.dp_threshold;
}

/**
 * @brief 计算前导零位数
 */
uint32_t PrecomputeTable::CountLeadingZeros(const uint8_t* hash) {
    uint32_t zeros = 0;
    for (int i = 0; i < 32; i++) {
        if (hash[i] == 0) {
            zeros += 8;
        } else {
            // 计算字节内的前导零
            uint8_t byte = hash[i];
            while ((byte & 0x80) == 0 && zeros < 256) {
                zeros++;
                byte <<= 1;
            }
            break;
        }
    }
    return zeros;
}
```

---

## 🎯 第二阶段：最优选择算法（论文第3.4节）

### 2.1 选择最有用的区分点

**理论基础**: 论文第3.4节"Choosing the most useful distinguished points"

```cpp
/**
 * @brief 选择最有用的区分点
 * 实现论文3.4节的权重优化算法
 */
std::vector<PrecomputeEntry> PrecomputeTable::SelectMostUseful(
    const std::vector<PrecomputeEntry>& candidates, 
    uint64_t target_size) {
    
    printf("从 %zu 个候选点中选择最有用的 %llu 个\n", candidates.size(), target_size);
    
    // 步骤1: 合并相同区分点的统计信息
    std::unordered_map<uint64_t, PrecomputeEntry> merged_candidates;
    
    for (const auto& candidate : candidates) {
        uint64_t hash = candidate.hash_value;
        
        if (merged_candidates.find(hash) == merged_candidates.end()) {
            merged_candidates[hash] = candidate;
        } else {
            // 合并统计信息
            auto& existing = merged_candidates[hash];
            existing.walk_length += candidate.walk_length;
            existing.walk_count += candidate.walk_count;
            
            // 重新计算权重（论文公式）
            existing.weight = existing.walk_length + 4.0 * params.W * existing.walk_count;
        }
    }
    
    // 步骤2: 转换为向量并排序
    std::vector<PrecomputeEntry> unique_candidates;
    unique_candidates.reserve(merged_candidates.size());
    
    for (const auto& pair : merged_candidates) {
        unique_candidates.push_back(pair.second);
    }
    
    // 步骤3: 按权重降序排序（论文3.4节策略）
    std::sort(unique_candidates.begin(), unique_candidates.end(),
              [](const PrecomputeEntry& a, const PrecomputeEntry& b) {
                  return a.weight > b.weight;
              });
    
    // 步骤4: 选择前target_size个
    size_t actual_size = std::min((size_t)target_size, unique_candidates.size());
    unique_candidates.resize(actual_size);
    
    printf("实际选择了 %zu 个最有用的区分点\n", actual_size);
    
    // 统计信息
    double total_weight = 0.0;
    uint64_t total_walks = 0;
    for (const auto& entry : unique_candidates) {
        total_weight += entry.weight;
        total_walks += entry.walk_count;
    }
    
    printf("平均权重: %.2f, 平均游走数: %.2f\n", 
           total_weight / actual_size, (double)total_walks / actual_size);
    
    return unique_candidates;
}
```

### 2.2 权重计算优化

**理论基础**: 论文中的启发式权重估计

```cpp
/**
 * @brief 计算区分点权重
 * 基于论文的启发式估计方法
 */
double PrecomputeTable::CalculateWeight(const PrecomputeEntry& entry) {
    // 论文公式: weight = total_walk_length + 4W·walk_count
    double base_weight = entry.walk_length + 4.0 * params.W * entry.walk_count;
    
    // 额外优化: 考虑祖先数量估计
    double ancestor_bonus = 0.0;
    if (entry.walk_count > 1) {
        // 多个游走到达同一点，说明该点有更多祖先
        ancestor_bonus = log(entry.walk_count) * params.W;
    }
    
    return base_weight + ancestor_bonus;
}
```

---

## 💾 第三阶段：空间优化实现（论文第5节）

### 3.1 哈希压缩（论文第5.2节）

**理论基础**: 论文第5.2节"Hash-and-check"

```cpp
/**
 * @brief 哈希压缩实现
 * 基于论文5.2节的哈希存储策略
 */
void PrecomputeTable::HashCompress() {
    printf("开始哈希压缩...\n");
    
    size_t original_size = GetMemoryUsage();
    
    for (auto& entry : entries) {
        if (!entry.is_compressed) {
            // 计算区分点的哈希值
            entry.hash_value = ComputePointHash(entry.distinguished_point);
            
            // 清除原始点数据（节省内存）
            entry.distinguished_point = Point();  // 清空
            entry.is_compressed = true;
        }
    }
    
    size_t compressed_size = GetMemoryUsage();
    double ratio = (double)original_size / compressed_size;
    
    printf("哈希压缩完成: %.2fx (原始: %zu MB -> 压缩: %zu MB)\n", 
           ratio, original_size / (1024*1024), compressed_size / (1024*1024));
    
    is_compressed = true;
}

/**
 * @brief 计算椭圆曲线点的哈希值
 */
uint64_t PrecomputeTable::ComputePointHash(const Point& P) {
    uint8_t hash[32];
    SHA256_CTX ctx;
    SHA256_Init(&ctx);
    
    uint8_t x_bytes[32], y_bytes[32];
    P.x.Get32Bytes(x_bytes);
    P.y.Get32Bytes(y_bytes);
    
    SHA256_Update(&ctx, x_bytes, 32);
    SHA256_Update(&ctx, y_bytes, 32);
    SHA256_Final(hash, &ctx);
    
    // 取前64位作为哈希值
    return *((uint64_t*)hash);
}
```

### 3.2 增量压缩（论文第5.3节）

**理论基础**: 论文第5.3节"Delta compression of sorted hashes"

```cpp
/**
 * @brief 增量压缩实现
 * 基于论文5.3节的差值存储策略
 */
void PrecomputeTable::DeltaCompress() {
    printf("开始增量压缩...\n");
    
    // 步骤1: 按哈希值排序
    std::sort(entries.begin(), entries.end(),
              [](const PrecomputeEntry& a, const PrecomputeEntry& b) {
                  return a.hash_value < b.hash_value;
              });
    
    // 步骤2: 计算差值
    std::vector<uint64_t> deltas;
    deltas.reserve(entries.size());
    
    if (!entries.empty()) {
        deltas.push_back(entries[0].hash_value);  // 第一个值保持不变
        
        for (size_t i = 1; i < entries.size(); i++) {
            uint64_t delta = entries[i].hash_value - entries[i-1].hash_value;
            deltas.push_back(delta);
        }
    }
    
    // 步骤3: 分析压缩效果
    size_t original_bits = entries.size() * 64;  // 原始64位哈希
    size_t compressed_bits = EstimateCompressedSize(deltas);
    
    double compression_ratio = (double)original_bits / compressed_bits;
    
    printf("增量压缩效果: %.2fx (原始: %zu bits -> 压缩: %zu bits)\n", 
           compression_ratio, original_bits, compressed_bits);
}

/**
 * @brief 估计压缩后大小
 */
size_t PrecomputeTable::EstimateCompressedSize(const std::vector<uint64_t>& deltas) {
    size_t total_bits = 0;
    
    for (uint64_t delta : deltas) {
        if (delta == 0) {
            total_bits += 1;  // 特殊编码
        } else {
            // 使用变长编码
            total_bits += 64 - __builtin_clzll(delta) + 1;
        }
    }
    
    return total_bits;
}
```

### 3.3 种子重构（论文第5.4节）

**理论基础**: 论文第5.4节"Compressing discrete logs themselves"

```cpp
/**
 * @brief 种子重构实现
 * 基于论文5.4节的种子存储策略
 */
void PrecomputeTable::SeedCompress() {
    printf("开始种子压缩...\n");
    
    for (auto& entry : entries) {
        // 生成重构种子
        entry.reconstruction_seed = GenerateReconstructionSeed(entry);
        
        // 压缩离散对数（只保留高位）
        uint32_t high_bits = 32;  // 保留32位高位
        entry.compressed_log = (uint32_t)(entry.discrete_log.GetInt64() >> (64 - high_bits));
    }
    
    printf("种子压缩完成\n");
}

/**
 * @brief 生成重构种子
 */
uint16_t PrecomputeTable::GenerateReconstructionSeed(const PrecomputeEntry& entry) {
    // 基于离散对数生成确定性种子
    uint64_t log_value = entry.discrete_log.GetInt64();
    return (uint16_t)(log_value ^ (log_value >> 16) ^ (log_value >> 32) ^ (log_value >> 48));
}

/**
 * @brief 重构完整离散对数
 * 实现论文5.4节的重构算法
 */
Int PrecomputeTable::ReconstructDiscreteLog(const PrecomputeEntry& entry) const {
    if (!entry.is_compressed) {
        return entry.discrete_log;
    }
    
    // 步骤1: 从种子重构低位
    uint64_t high_part = ((uint64_t)entry.compressed_log) << 32;
    
    // 步骤2: 通过重新游走确定低位
    Random::SetSeed(entry.reconstruction_seed);
    Int start_log(high_part);
    
    // 重新执行游走过程
    Point P = secp->ComputePublicKey(&start_log);
    Int current_log = start_log;
    
    for (uint32_t step = 0; step < entry.walk_length; step++) {
        uint32_t jump_index = Random::GetRandomInt(jump_table.size());
        P = secp->Add(P, jump_table[jump_index]);
        current_log = current_log.Add(&jump_distances[jump_index]);
        
        // 检查是否匹配原始哈希
        if (ComputePointHash(P) == entry.hash_value) {
            return current_log;
        }
    }
    
    // 重构失败，返回近似值
    return Int(high_part);
}
```

---

## 🚀 第四阶段：GPU并行化实现

### 4.1 CUDA预计算表生成内核

**理论基础**: 将论文算法移植到GPU并行执行

```cpp
/**
 * @brief GPU预计算表生成内核
 * 并行生成候选区分点
 */
__global__ void precompute_table_generation_kernel(
    uint64_t* candidate_hashes,      // 输出：候选点哈希
    uint64_t* candidate_logs,        // 输出：候选点离散对数
    uint32_t* walk_lengths,          // 输出：游走长度
    uint32_t* walk_counts,           // 输出：游走计数
    uint64_t ell,                    // 搜索区间长度
    uint64_t W,                      // 最大游走长度
    uint32_t dp_threshold,           // 区分点阈值
    uint64_t* jump_table_x,          // 跳跃表x坐标
    uint64_t* jump_table_y,          // 跳跃表y坐标
    uint64_t* jump_distances,        // 跳跃距离
    uint32_t jump_table_size,        // 跳跃表大小
    uint32_t total_candidates        // 总候选数M
) {
    uint32_t tid = blockIdx.x * blockDim.x + threadIdx.x;
    if (tid >= total_candidates) return;

    // 初始化随机数生成器
    curandState state;
    curand_init(tid + blockIdx.x * 1000000ULL, 0, 0, &state);

    // 随机起始点 y_i ∈ [0, ℓ-1]
    uint64_t y_start = curand(&state) % ell;

    // 计算起始椭圆曲线点
    ECPoint P;
    compute_public_key_gpu(y_start, &P);

    uint64_t total_steps = 0;
    uint64_t current_log = y_start;
    uint32_t walk_count = 1;

    // 游走直到找到区分点
    while (total_steps < W * 4) {
        // 检查是否为区分点
        if (is_distinguished_point_gpu(P, dp_threshold)) {
            // 计算哈希值
            uint64_t hash = compute_point_hash_gpu(P);

            // 原子操作检查是否已存在
            uint64_t old_hash = atomicCAS(&candidate_hashes[tid], 0, hash);
            if (old_hash == 0) {
                // 首次发现此点
                candidate_logs[tid] = current_log;
                walk_lengths[tid] = total_steps;
                walk_counts[tid] = walk_count;
            } else if (old_hash == hash) {
                // 相同点，增加计数
                atomicAdd(&walk_counts[tid], 1);
                atomicAdd(&walk_lengths[tid], total_steps);
            }
            return;
        }

        // r-adding walk步进
        uint32_t jump_index = curand(&state) % jump_table_size;
        ECPoint jump_point = {jump_table_x[jump_index], jump_table_y[jump_index]};

        elliptic_add_gpu(&P, &jump_point, &P);
        current_log += jump_distances[jump_index];
        total_steps++;
    }

    // 未找到区分点，标记为无效
    candidate_hashes[tid] = 0;
}

/**
 * @brief GPU区分点检测
 */
__device__ bool is_distinguished_point_gpu(const ECPoint& P, uint32_t threshold) {
    uint8_t hash[32];

    // GPU SHA256计算
    sha256_gpu(P.x, P.y, hash);

    // 计算前导零
    uint32_t zeros = 0;
    for (int i = 0; i < 32 && zeros < threshold; i++) {
        if (hash[i] == 0) {
            zeros += 8;
        } else {
            uint8_t byte = hash[i];
            while ((byte & 0x80) == 0 && zeros < threshold) {
                zeros++;
                byte <<= 1;
            }
            break;
        }
    }

    return zeros >= threshold;
}
```

### 4.2 GPU内存管理优化

```cpp
/**
 * @brief GPU预计算表生成主函数
 */
bool PrecomputeTable::GenerateTableGPU(const TableGenParams& params) {
    printf("开始GPU预计算表生成...\n");

    // 计算GPU参数
    uint64_t M = (uint64_t)ceil(params.T * log(2.0));
    uint32_t threads_per_block = 256;
    uint32_t blocks = (M + threads_per_block - 1) / threads_per_block;

    // 分配GPU内存
    uint64_t* d_candidate_hashes;
    uint64_t* d_candidate_logs;
    uint32_t* d_walk_lengths;
    uint32_t* d_walk_counts;

    cudaMalloc(&d_candidate_hashes, M * sizeof(uint64_t));
    cudaMalloc(&d_candidate_logs, M * sizeof(uint64_t));
    cudaMalloc(&d_walk_lengths, M * sizeof(uint32_t));
    cudaMalloc(&d_walk_counts, M * sizeof(uint32_t));

    // 初始化为0
    cudaMemset(d_candidate_hashes, 0, M * sizeof(uint64_t));
    cudaMemset(d_candidate_logs, 0, M * sizeof(uint64_t));
    cudaMemset(d_walk_lengths, 0, M * sizeof(uint32_t));
    cudaMemset(d_walk_counts, 0, M * sizeof(uint32_t));

    // 准备跳跃表数据
    uint64_t* d_jump_table_x, *d_jump_table_y, *d_jump_distances;
    PrepareJumpTableGPU(&d_jump_table_x, &d_jump_table_y, &d_jump_distances);

    // 启动内核
    precompute_table_generation_kernel<<<blocks, threads_per_block>>>(
        d_candidate_hashes, d_candidate_logs, d_walk_lengths, d_walk_counts,
        params.ell, params.W, params.dp_threshold,
        d_jump_table_x, d_jump_table_y, d_jump_distances, jump_table.size(),
        M
    );

    cudaDeviceSynchronize();

    // 复制结果回CPU
    std::vector<uint64_t> h_hashes(M), h_logs(M);
    std::vector<uint32_t> h_lengths(M), h_counts(M);

    cudaMemcpy(h_hashes.data(), d_candidate_hashes, M * sizeof(uint64_t), cudaMemcpyDeviceToHost);
    cudaMemcpy(h_logs.data(), d_candidate_logs, M * sizeof(uint64_t), cudaMemcpyDeviceToHost);
    cudaMemcpy(h_lengths.data(), d_walk_lengths, M * sizeof(uint32_t), cudaMemcpyDeviceToHost);
    cudaMemcpy(h_counts.data(), d_walk_counts, M * sizeof(uint32_t), cudaMemcpyDeviceToHost);

    // 处理结果
    std::vector<PrecomputeEntry> candidates;
    for (uint64_t i = 0; i < M; i++) {
        if (h_hashes[i] != 0) {  // 有效候选点
            PrecomputeEntry entry;
            entry.hash_value = h_hashes[i];
            entry.discrete_log = Int(h_logs[i]);
            entry.walk_length = h_lengths[i];
            entry.walk_count = h_counts[i];
            entry.weight = h_lengths[i] + 4.0 * params.W * h_counts[i];
            entry.is_compressed = true;  // GPU版本直接生成压缩格式

            candidates.push_back(entry);
        }
    }

    printf("GPU生成了 %zu 个有效候选点\n", candidates.size());

    // 选择最有用的T个点
    entries = SelectMostUseful(candidates, params.T);

    // 清理GPU内存
    cudaFree(d_candidate_hashes);
    cudaFree(d_candidate_logs);
    cudaFree(d_walk_lengths);
    cudaFree(d_walk_counts);
    cudaFree(d_jump_table_x);
    cudaFree(d_jump_table_y);
    cudaFree(d_jump_distances);

    is_built = true;
    return true;
}
```

---

## 📊 第五阶段：性能优化与测试

### 5.1 查找优化

```cpp
/**
 * @brief 优化的碰撞查找算法
 * 使用哈希索引加速查找
 */
const PrecomputeEntry* PrecomputeTable::FindCollision(const Point& point) const {
    if (!is_built) return nullptr;

    uint64_t hash = ComputePointHash(point);

    // 快速哈希查找
    auto it = hash_index.find(hash);
    if (it != hash_index.end()) {
        const PrecomputeEntry& entry = entries[it->second];

        // 如果是压缩存储，需要验证
        if (entry.is_compressed) {
            // 哈希匹配即认为找到（小概率误判）
            return &entry;
        } else {
            // 精确匹配验证
            if (secp->EC(entry.distinguished_point) == secp->EC(point)) {
                return &entry;
            }
        }
    }

    return nullptr;
}

/**
 * @brief 建立哈希索引
 */
void PrecomputeTable::BuildHashIndex() {
    hash_index.clear();
    hash_index.reserve(entries.size());

    for (size_t i = 0; i < entries.size(); i++) {
        hash_index[entries[i].hash_value] = i;
    }

    printf("建立了 %zu 个条目的哈希索引\n", entries.size());
}
```

### 5.2 性能基准测试

```cpp
/**
 * @brief 预计算表性能测试
 */
void PrecomputeTable::BenchmarkPerformance() {
    if (!is_built) {
        printf("表未构建，无法进行性能测试\n");
        return;
    }

    printf("=== 预计算表性能基准测试 ===\n");

    // 测试1: 查找性能
    const int test_queries = 10000;
    auto start = std::chrono::high_resolution_clock::now();

    int found_count = 0;
    for (int i = 0; i < test_queries; i++) {
        // 生成随机测试点
        Int random_log = Random::GetRandomInt(params.ell);
        Point test_point = secp->ComputePublicKey(&random_log);

        if (FindCollision(test_point) != nullptr) {
            found_count++;
        }
    }

    auto end = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);

    double queries_per_second = (double)test_queries * 1000000.0 / duration.count();
    double hit_rate = (double)found_count / test_queries * 100.0;

    printf("查找性能: %.2f 查询/秒, 命中率: %.2f%%\n", queries_per_second, hit_rate);

    // 测试2: 内存使用统计
    size_t memory_usage = GetMemoryUsage();
    double memory_per_entry = (double)memory_usage / entries.size();

    printf("内存使用: %zu MB (%.2f KB/条目)\n",
           memory_usage / (1024*1024), memory_per_entry / 1024.0);

    // 测试3: 理论性能验证
    double theoretical_success_prob = 1.0 - exp(-params.alpha * params.alpha);
    printf("理论成功概率: %.2f%% (α=%.3f)\n",
           theoretical_success_prob * 100.0, params.alpha);

    // 测试4: 压缩效果
    if (is_compressed) {
        double compression_ratio = GetCompressionRatio();
        printf("压缩比: %.2fx\n", compression_ratio);
    }
}
```

---

## 📋 实施时间表

### 第1周：基础框架搭建
- [ ] 创建PrecomputeTable类基础结构
- [ ] 实现基本的数据结构和接口
- [ ] 集成到现有Kangaroo项目中

### 第2周：核心算法实现
- [ ] 实现预计算表生成算法（论文3.2节）
- [ ] 实现Distinguished Points检测
- [ ] 实现r-adding walks跳跃表

### 第3周：优化算法实现
- [ ] 实现最有用区分点选择（论文3.4节）
- [ ] 实现权重计算和排序算法
- [ ] 添加统计信息和调试输出

### 第4周：空间优化实现
- [ ] 实现哈希压缩（论文5.2节）
- [ ] 实现增量压缩（论文5.3节）
- [ ] 实现种子重构（论文5.4节）

### 第5周：GPU并行化
- [ ] 设计CUDA内核架构
- [ ] 实现GPU预计算表生成
- [ ] 优化GPU内存访问模式

### 第6周：集成测试与优化
- [ ] 性能基准测试
- [ ] 与现有系统集成测试
- [ ] 参数调优和性能优化

---

## 🎯 验收标准

### 功能验收
- [ ] 能够生成符合论文规格的预计算表
- [ ] 查找性能达到理论预期
- [ ] 空间压缩比达到论文水平（>10x）
- [ ] GPU版本性能比CPU版本提升>10倍

### 性能验收
- [ ] 小规模测试（ℓ=2⁴⁸）成功率>95%
- [ ] 大规模测试（ℓ=2⁶⁰）能够正常运行
- [ ] 内存使用量在合理范围内
- [ ] 与现有Kangaroo系统无缝集成

### 质量验收
- [ ] 代码符合项目规范
- [ ] 完整的单元测试覆盖
- [ ] 详细的性能基准报告
- [ ] 完整的用户文档

通过这个详细的执行计划，我们将能够完美实现Bernstein-Lange论文中的预计算表算法，为CUDA-BSGS Kangaroo项目提供强大的性能提升。
```
