#ifndef PRECOMPUTE_TABLE_H
#define PRECOMPUTE_TABLE_H

#include "SECPK1/Point.h"
#include "SECPK1/Int.h"
#include <vector>
#include <unordered_map>
#include <string>
#include <memory>

/**
 * @brief Bernstein-Lange预计算表条目
 * 基于论文第3.2节设计
 */
struct PrecomputeEntry {
    Point distinguished_point;    // 区分点 (椭圆曲线点)
    Int discrete_log;            // 对应的离散对数
    uint64_t walk_length;        // 游走长度
    uint32_t walk_count;         // 到达此点的游走数量
    double weight;               // 权重 = walk_length + 4W·walk_count
    uint64_t hash_value;         // 点的哈希值（用于快速查找）
    
    // 压缩存储相关
    bool is_compressed;          // 是否已压缩
    uint32_t compressed_log;     // 压缩的离散对数
    uint16_t reconstruction_seed; // 重构种子
};

/**
 * @brief 预计算表生成参数
 * 基于论文公式计算
 */
struct TableGenParams {
    uint64_t ell;               // 搜索区间长度 ℓ
    uint64_t T;                 // 目标表大小 = ℓ¹ᐟ³
    uint64_t M;                 // 候选点数量 = ⌈T·ln(2)⌉
    uint64_t W;                 // 游走长度 = α·(ℓ/T)¹ᐟ²
    double alpha;               // 优化参数 ≈ 0.786
    uint32_t r;                 // r-adding walks参数
    uint32_t dp_threshold;      // 区分点阈值位数
    double dp_probability;      // 区分点概率 = 1/W
    
    // GPU相关参数
    uint32_t gpu_threads;       // GPU线程数
    uint32_t gpu_blocks;        // GPU块数
    size_t gpu_memory_limit;    // GPU内存限制
};

/**
 * @brief Bernstein-Lange预计算表类
 * 实现论文第3.2-3.4节算法
 */
class PrecomputeTable {
private:
    std::vector<PrecomputeEntry> entries;
    std::unordered_map<uint64_t, size_t> hash_index; // 哈希到索引的映射
    TableGenParams params;
    bool is_built;
    bool is_compressed;
    
    // 跳跃表（r-adding walks）
    std::vector<Point> jump_table;
    std::vector<Int> jump_distances;
    
public:
    PrecomputeTable();
    ~PrecomputeTable();
    
    // === 核心算法实现 ===
    
    /**
     * @brief 生成预计算表（论文算法3.2）
     * @param params 生成参数
     * @return 成功返回true
     */
    bool GenerateTable(const TableGenParams& params);
    
    /**
     * @brief 选择最有用的区分点（论文3.4节）
     * @param candidates 候选点列表
     * @param target_size 目标大小T
     * @return 选中的条目
     */
    std::vector<PrecomputeEntry> SelectMostUseful(
        const std::vector<PrecomputeEntry>& candidates, 
        uint64_t target_size);
    
    /**
     * @brief 查找碰撞
     * @param point 查询点
     * @return 找到的条目指针，未找到返回nullptr
     */
    const PrecomputeEntry* FindCollision(const Point& point) const;
    
    // === 空间优化（论文第5节）===
    
    /**
     * @brief 压缩表（论文5.2-5.4节）
     */
    void CompressTable();
    
    /**
     * @brief 哈希压缩（论文5.2节）
     */
    void HashCompress();
    
    /**
     * @brief 增量压缩（论文5.3节）
     */
    void DeltaCompress();
    
    /**
     * @brief 种子重构（论文5.4节）
     */
    Int ReconstructDiscreteLog(const PrecomputeEntry& entry) const;
    
    // === 文件I/O ===
    
    bool SaveToFile(const std::string& filename) const;
    bool LoadFromFile(const std::string& filename);
    bool SaveCompressed(const std::string& filename) const;
    bool LoadCompressed(const std::string& filename);
    
    // === 统计信息 ===
    
    size_t GetSize() const { return entries.size(); }
    size_t GetMemoryUsage() const;
    double GetCompressionRatio() const;
    void PrintStatistics() const;
    
    // === GPU支持 ===
    
    bool GenerateTableGPU(const TableGenParams& params);
    void* GetGPUData() const;
    size_t GetGPUDataSize() const;
};

#endif // PRECOMPUTE_TABLE_H
